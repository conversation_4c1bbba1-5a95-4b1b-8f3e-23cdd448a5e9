import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../services/flashcard_service.dart';
import '../services/audio_service.dart';
import '../models/flashcard.dart';
import '../theme/app_theme.dart';

class FlashcardViewerScreen extends StatefulWidget {
  const FlashcardViewerScreen({super.key});

  @override
  State<FlashcardViewerScreen> createState() => _FlashcardViewerScreenState();
}

class _FlashcardViewerScreenState extends State<FlashcardViewerScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final flashcardService = context.read<FlashcardService>();
      final audioService = context.read<AudioService>();
      await flashcardService.initialize();
      await audioService.initialize();
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _playAudio(String word) {
    final audioService = context.read<AudioService>();
    audioService.speak(word);
  }

  Widget _buildEmptyState() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flashcards'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.style_outlined, size: 80, color: AppTheme.textSecondary),
            SizedBox(height: 16),
            Text(
              'No flashcards yet',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppTheme.textSecondary,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Go to Word List to add your first flashcard',
              style: TextStyle(fontSize: 16, color: AppTheme.textSecondary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flashcards'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryColor),
      ),
    );
  }

  Widget _buildFlashcardImage(Flashcard flashcard) {
    final imagePath = flashcard.displayImagePath;

    if (imagePath == null || imagePath.isEmpty) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: AppTheme.backgroundColor,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_outlined,
                size: 80,
                color: AppTheme.textSecondary,
              ),
              SizedBox(height: 16),
              Text(
                'No image',
                style: TextStyle(fontSize: 18, color: AppTheme.textSecondary),
              ),
            ],
          ),
        ),
      );
    }

    // Check if it's a local file or network URL
    if (imagePath.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: imagePath,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        placeholder: (context, url) => Container(
          color: AppTheme.backgroundColor,
          child: const Center(
            child: CircularProgressIndicator(color: AppTheme.primaryColor),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: AppTheme.backgroundColor,
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 80, color: AppTheme.errorColor),
                SizedBox(height: 16),
                Text(
                  'Failed to load image',
                  style: TextStyle(fontSize: 18, color: AppTheme.errorColor),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      // Local file
      final file = File(imagePath);
      if (file.existsSync()) {
        return Image.file(
          file,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) => Container(
            color: AppTheme.backgroundColor,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 80,
                    color: AppTheme.errorColor,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Failed to load image',
                    style: TextStyle(fontSize: 18, color: AppTheme.errorColor),
                  ),
                ],
              ),
            ),
          ),
        );
      } else {
        return Container(
          color: AppTheme.backgroundColor,
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_not_supported_outlined,
                  size: 80,
                  color: AppTheme.textSecondary,
                ),
                SizedBox(height: 16),
                Text(
                  'Image not found',
                  style: TextStyle(fontSize: 18, color: AppTheme.textSecondary),
                ),
              ],
            ),
          ),
        );
      }
    }
  }

  Widget _buildFlashcard(Flashcard flashcard) {
    return Stack(
      children: [
        // Background image
        _buildFlashcardImage(flashcard),

        // Gradient overlay for better text readability
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 200,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Colors.black54, Colors.black87],
              ),
            ),
          ),
        ),

        // Word label and audio button
        Positioned(
          bottom: 60,
          left: 0,
          right: 0,
          child: Column(
            children: [
              // Word text
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                child: Text(
                  flashcard.word,
                  style: const TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(2, 2),
                        blurRadius: 4,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 16),

              // Audio button
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () => _playAudio(flashcard.word),
                  icon: const Icon(
                    Icons.volume_up,
                    color: Colors.white,
                    size: 32,
                  ),
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return _buildLoadingState();
    }

    return Consumer<FlashcardService>(
      builder: (context, flashcardService, child) {
        if (flashcardService.isLoading) {
          return _buildLoadingState();
        }

        if (flashcardService.isEmpty) {
          return _buildEmptyState();
        }

        final flashcards = flashcardService.flashcards;

        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Flashcards (${_currentIndex + 1}/${flashcards.length})',
            ),
            backgroundColor: AppTheme.primaryColor,
          ),
          body: PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: flashcards.length,
            itemBuilder: (context, index) {
              return _buildFlashcard(flashcards[index]);
            },
          ),
        );
      },
    );
  }
}
