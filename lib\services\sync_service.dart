import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'flashcard_service.dart';
import 'firebase_service.dart';
import '../models/flashcard.dart';

class SyncService {
  final FlashcardService _flashcardService;
  final FirebaseService _firebaseService;
  final Connectivity _connectivity = Connectivity();

  SyncService(this._flashcardService, this._firebaseService);

  // Check if device has internet connection
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return !(connectivityResult == ConnectivityResult.none);
    } catch (e) {
      debugPrint('Connectivity check error: $e');
      return false;
    }
  }

  // Sync all data from Firebase to local storage (download)
  Future<SyncResult> syncFromFirebase({bool forceOverwrite = false}) async {
    try {
      if (!await hasInternetConnection()) {
        return SyncResult(
          success: false,
          message: 'No internet connection',
          syncedCount: 0,
        );
      }

      // Get all flashcards from Firebase
      final firebaseFlashcards = await _firebaseService.getAllFlashcards();

      if (firebaseFlashcards.isEmpty) {
        return SyncResult(
          success: true,
          message: 'No flashcards found on server',
          syncedCount: 0,
        );
      }

      if (forceOverwrite) {
        // Clear local storage and insert all Firebase data
        await _flashcardService.clearAllFlashcards();
        await _flashcardService.bulkInsertFlashcards(firebaseFlashcards);

        return SyncResult(
          success: true,
          message: 'Downloaded ${firebaseFlashcards.length} flashcards',
          syncedCount: firebaseFlashcards.length,
        );
      } else {
        // Merge with local data (keep newer versions)
        final localFlashcards = _flashcardService.flashcards;
        final mergedFlashcards = <String, Flashcard>{};

        // Add local flashcards to map
        for (final flashcard in localFlashcards) {
          mergedFlashcards[flashcard.id] = flashcard;
        }

        int updatedCount = 0;
        // Update with Firebase flashcards if they're newer
        for (final firebaseFlashcard in firebaseFlashcards) {
          final localFlashcard = mergedFlashcards[firebaseFlashcard.id];

          if (localFlashcard == null ||
              firebaseFlashcard.updatedAt.isAfter(localFlashcard.updatedAt)) {
            mergedFlashcards[firebaseFlashcard.id] = firebaseFlashcard;
            updatedCount++;
          }
        }

        // Save merged data
        await _flashcardService.clearAllFlashcards();
        await _flashcardService.bulkInsertFlashcards(
          mergedFlashcards.values.toList(),
        );

        return SyncResult(
          success: true,
          message: 'Synced $updatedCount flashcards',
          syncedCount: updatedCount,
        );
      }
    } catch (e) {
      debugPrint('Sync from Firebase error: $e');
      return SyncResult(
        success: false,
        message: 'Sync failed: $e',
        syncedCount: 0,
      );
    }
  }

  // Sync local changes to Firebase (upload)
  Future<SyncResult> syncToFirebase() async {
    try {
      if (!await hasInternetConnection()) {
        return SyncResult(
          success: false,
          message: 'No internet connection',
          syncedCount: 0,
        );
      }

      // Get unsynced flashcards
      final unsyncedFlashcards = _flashcardService.getUnsyncedFlashcards();

      if (unsyncedFlashcards.isEmpty) {
        return SyncResult(
          success: true,
          message: 'All flashcards are already synced',
          syncedCount: 0,
        );
      }

      int syncedCount = 0;
      final errors = <String>[];

      for (final flashcard in unsyncedFlashcards) {
        try {
          // Check if flashcard exists in Firebase
          final exists = await _firebaseService.flashcardExists(flashcard.id);

          bool success;
          if (exists) {
            success = await _firebaseService.updateFlashcard(flashcard);
          } else {
            success = await _firebaseService.saveFlashcard(flashcard);
          }

          if (success) {
            await _flashcardService.markAsSynced(flashcard.id);
            syncedCount++;
          } else {
            errors.add('Failed to sync "${flashcard.word}"');
          }
        } catch (e) {
          errors.add('Error syncing "${flashcard.word}": $e');
        }
      }

      if (errors.isNotEmpty) {
        return SyncResult(
          success: false,
          message: 'Partial sync: $syncedCount synced, ${errors.length} failed',
          syncedCount: syncedCount,
          errors: errors,
        );
      }

      return SyncResult(
        success: true,
        message: 'Synced $syncedCount flashcards to server',
        syncedCount: syncedCount,
      );
    } catch (e) {
      debugPrint('Sync to Firebase error: $e');
      return SyncResult(
        success: false,
        message: 'Sync failed: $e',
        syncedCount: 0,
      );
    }
  }

  // Full bidirectional sync
  Future<SyncResult> fullSync() async {
    try {
      if (!await hasInternetConnection()) {
        return SyncResult(
          success: false,
          message: 'No internet connection',
          syncedCount: 0,
        );
      }

      // First, upload local changes
      final uploadResult = await syncToFirebase();

      // Then, download latest from Firebase
      final downloadResult = await syncFromFirebase();

      if (uploadResult.success && downloadResult.success) {
        return SyncResult(
          success: true,
          message:
              'Full sync completed: ${uploadResult.syncedCount} uploaded, ${downloadResult.syncedCount} downloaded',
          syncedCount: uploadResult.syncedCount + downloadResult.syncedCount,
        );
      } else {
        final errors = <String>[];
        if (!uploadResult.success)
          errors.add('Upload: ${uploadResult.message}');
        if (!downloadResult.success)
          errors.add('Download: ${downloadResult.message}');

        return SyncResult(
          success: false,
          message: 'Partial sync completed',
          syncedCount: uploadResult.syncedCount + downloadResult.syncedCount,
          errors: errors,
        );
      }
    } catch (e) {
      debugPrint('Full sync error: $e');
      return SyncResult(
        success: false,
        message: 'Full sync failed: $e',
        syncedCount: 0,
      );
    }
  }

  // Get sync status
  Future<SyncStatus> getSyncStatus() async {
    try {
      final hasInternet = await hasInternetConnection();
      final unsyncedCount = _flashcardService.getUnsyncedFlashcards().length;
      final totalCount = _flashcardService.count;

      DateTime? lastSyncTime;
      try {
        final syncInfo = await _firebaseService.getSyncInfo();
        lastSyncTime = syncInfo['lastUpdated'] as DateTime?;
      } catch (e) {
        debugPrint('Get sync info error: $e');
      }

      return SyncStatus(
        hasInternetConnection: hasInternet,
        unsyncedCount: unsyncedCount,
        totalCount: totalCount,
        lastSyncTime: lastSyncTime,
        isFullySynced: unsyncedCount == 0,
      );
    } catch (e) {
      debugPrint('Get sync status error: $e');
      return SyncStatus(
        hasInternetConnection: false,
        unsyncedCount: 0,
        totalCount: 0,
        lastSyncTime: null,
        isFullySynced: false,
      );
    }
  }
}

class SyncResult {
  final bool success;
  final String message;
  final int syncedCount;
  final List<String>? errors;

  SyncResult({
    required this.success,
    required this.message,
    required this.syncedCount,
    this.errors,
  });

  @override
  String toString() {
    return 'SyncResult{success: $success, message: $message, syncedCount: $syncedCount}';
  }
}

class SyncStatus {
  final bool hasInternetConnection;
  final int unsyncedCount;
  final int totalCount;
  final DateTime? lastSyncTime;
  final bool isFullySynced;

  SyncStatus({
    required this.hasInternetConnection,
    required this.unsyncedCount,
    required this.totalCount,
    this.lastSyncTime,
    required this.isFullySynced,
  });

  double get syncProgress {
    if (totalCount == 0) return 1.0;
    return (totalCount - unsyncedCount) / totalCount;
  }

  @override
  String toString() {
    return 'SyncStatus{hasInternet: $hasInternetConnection, unsynced: $unsyncedCount/$totalCount, isFullySynced: $isFullySynced}';
  }
}
