import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class FamilyIdUtils {
  static const String _familyIdKey = 'family_id';

  static Future<String> getFamilyId() async {
    final prefs = await SharedPreferences.getInstance();
    String? id = prefs.getString(_familyIdKey);
    if (id == null || id.isEmpty) {
      id = const Uuid().v4();
      await prefs.setString(_familyIdKey, id);
    }
    return id;
  }

  static Future<void> setFamilyId(String id) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_familyIdKey, id);
  }

  static Future<void> clearFamilyId() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_familyIdKey);
  }
}
