import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'firebase_options.dart';
import 'services/flashcard_service.dart';
import 'services/firebase_service.dart';
import 'services/audio_service.dart';
import 'services/sync_service.dart';
import 'models/flashcard.dart';
import 'models/category.dart' as model;
import 'screens/main_screen.dart';
import 'theme/app_theme.dart';
import 'utils/permissions.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize Hive
    await Hive.initFlutter();
    Hive.registerAdapter(FlashcardAdapter());
    Hive.registerAdapter(model.CategoryAdapter());

    // Request permissions
    await PermissionUtils.requestAllPermissions();

    // Initialize FlashcardService (load categories, etc.)
    final flashcardService = FlashcardService();
    await flashcardService.initialize();

    runApp(const EnglishForKidsApp());
  } catch (e) {
    debugPrint('Initialization error: $e');
    runApp(const ErrorApp(error: 'Failed to initialize app'));
  }
}

class EnglishForKidsApp extends StatelessWidget {
  const EnglishForKidsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => FlashcardService()),
        Provider(create: (_) => FirebaseService()),
        Provider(create: (_) => AudioService()),
        ProxyProvider2<FlashcardService, FirebaseService, SyncService>(
          update: (_, flashcardService, firebaseService, __) =>
              SyncService(flashcardService, firebaseService),
        ),
      ],
      child: MaterialApp(
        title: 'English for Kids',
        theme: AppTheme.lightTheme,
        home: const MainScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class ErrorApp extends StatelessWidget {
  final String error;

  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'English for Kids - Error',
      theme: AppTheme.lightTheme,
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 80,
                color: AppTheme.errorColor,
              ),
              const SizedBox(height: 16),
              const Text(
                'Initialization Error',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // Restart the app
                  main();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
