import 'package:hive/hive.dart';

part 'flashcard.g.dart';

@HiveType(typeId: 0)
class Flashcard extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String word;

  @HiveField(2)
  String? localImagePath;

  @HiveField(3)
  String? firebaseImageUrl;

  @HiveField(4)
  DateTime createdAt;

  @HiveField(5)
  DateTime updatedAt;

  @HiveField(6)
  bool isSynced;

  @HiveField(7)
  String category;

  @HiveField(8)
  String familyId;

  Flashcard({
    required this.id,
    required this.word,
    this.localImagePath,
    this.firebaseImageUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
    required this.category,
    required this.familyId,
  });

  // Get the image path to display (prefer local, fallback to Firebase URL)
  String? get displayImagePath {
    if (localImagePath != null && localImagePath!.isNotEmpty) {
      return localImagePath;
    }
    return firebaseImageUrl;
  }

  // Check if flashcard has a valid image
  bool get hasImage {
    return displayImagePath != null && displayImagePath!.isNotEmpty;
  }

  // Convert to Map for Firebase (now stores image as base64 string)
  Map<String, dynamic> toFirebaseMap() {
    return {
      'id': id,
      'word': word,
      'imageBase64': firebaseImageUrl, // repurpose this field for base64 image
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'category': category,
      'familyId': familyId,
    };
  }

  // Create from Firebase Map
  factory Flashcard.fromFirebaseMap(Map<String, dynamic> map) {
    return Flashcard(
      id: map['id'] ?? '',
      word: map['word'] ?? '',
      firebaseImageUrl:
          map['imageBase64'], // repurpose this field for base64 image
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
      isSynced: true,
      category: map['category'] ?? 'All',
      familyId: map['familyId'] ?? '',
    );
  }

  // Create a copy with updated fields
  Flashcard copyWith({
    String? id,
    String? word,
    String? localImagePath,
    String? firebaseImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
    String? category,
    String? familyId,
  }) {
    return Flashcard(
      id: id ?? this.id,
      word: word ?? this.word,
      localImagePath: localImagePath ?? this.localImagePath,
      firebaseImageUrl: firebaseImageUrl ?? this.firebaseImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
      category: category ?? this.category,
      familyId: familyId ?? this.familyId,
    );
  }

  @override
  String toString() {
    return 'Flashcard{id: $id, word: $word, hasImage: $hasImage, isSynced: $isSynced}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Flashcard && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
