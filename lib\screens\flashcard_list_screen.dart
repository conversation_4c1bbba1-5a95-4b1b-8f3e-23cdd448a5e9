import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../services/flashcard_service.dart';
import '../services/firebase_service.dart';
import '../models/flashcard.dart';
import '../theme/app_theme.dart';
import 'add_edit_flashcard_screen.dart';

class FlashcardListScreen extends StatefulWidget {
  const FlashcardListScreen({super.key});

  @override
  State<FlashcardListScreen> createState() => _FlashcardListScreenState();
}

class _FlashcardListScreenState extends State<FlashcardListScreen> {
  bool _isInitialized = false;
  bool _isSyncing = false;
  Map<String, bool> _categoryEnabled = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final flashcardService = context.read<FlashcardService>();
      await flashcardService.initialize();
      // Initialize category enabled map
      final categories = flashcardService.categories;
      setState(() {
        for (final cat in categories) {
          _categoryEnabled[cat.id] = cat.enabled ?? true;
        }
        _isInitialized = true;
      });
    });
  }

  Future<void> _downloadFromFirebase() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      final firebaseService = context.read<FirebaseService>();
      final flashcardService = context.read<FlashcardService>();

      // Get all flashcards from Firebase
      final firebaseFlashcards = await firebaseService.getAllFlashcards();

      if (firebaseFlashcards.isNotEmpty) {
        // Clear local storage and insert Firebase data
        await flashcardService.clearAllFlashcards();
        await flashcardService.bulkInsertFlashcards(firebaseFlashcards);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Downloaded ${firebaseFlashcards.length} flashcards',
              ),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No flashcards found on server'),
              backgroundColor: AppTheme.textSecondary,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download failed: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  Future<void> _deleteFlashcard(Flashcard flashcard) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Flashcard'),
        content: Text('Are you sure you want to delete "${flashcard.word}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final flashcardService = context.read<FlashcardService>();
        final firebaseService = context.read<FirebaseService>();

        // Delete from Firebase first
        if (flashcard.isSynced && flashcard.firebaseImageUrl != null) {
          // Remove or replace any call to firebaseService.deleteImage. If image deletion is needed, implement it in FirebaseService or remove the call.
          // For now, we'll just delete the flashcard from Firebase.
          await firebaseService.deleteFlashcard(flashcard.id);
        }

        // Delete from local storage
        await flashcardService.deleteFlashcard(flashcard.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Deleted "${flashcard.word}"'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Delete failed: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    }
  }

  void _editFlashcard(Flashcard flashcard) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditFlashcardScreen(flashcard: flashcard),
      ),
    );
  }

  void _addFlashcard() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddEditFlashcardScreen()),
    );
  }

  void _toggleCategory(String categoryId, bool? value) {
    setState(() {
      _categoryEnabled[categoryId] = value ?? false;
    });
    final flashcardService = context.read<FlashcardService>();
    flashcardService.setCategoryEnabled(categoryId, value ?? false);
  }

  Widget _buildCategorySection(String categoryName, String categoryId, List<Flashcard> cards) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Checkbox(
              value: _categoryEnabled[categoryId] ?? true,
              onChanged: (val) => _toggleCategory(categoryId, val),
            ),
            Text(
              categoryName,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        DataTable(
          columns: const [
            DataColumn(label: Text('#')),
            DataColumn(label: Text('Word')),
            DataColumn(label: Text('Action')),
          ],
          rows: [
            for (int i = 0; i < cards.length; i++)
              DataRow(cells: [
                DataCell(Text('${i + 1}')),
                DataCell(Text(cards[i].word)),
                DataCell(Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: AppTheme.primaryColor),
                      onPressed: () => _editFlashcard(cards[i]),
                      tooltip: 'Edit',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: AppTheme.errorColor),
                      onPressed: () => _deleteFlashcard(cards[i]),
                      tooltip: 'Delete',
                    ),
                  ],
                )),
              ]),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildFlashcardThumbnail(Flashcard flashcard) {
    final imagePath = flashcard.displayImagePath;

    if (imagePath == null || imagePath.isEmpty) {
      return Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.image_outlined,
          color: AppTheme.textSecondary,
          size: 24,
        ),
      );
    }

    if (imagePath.startsWith('http')) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedNetworkImage(
          imageUrl: imagePath,
          width: 60,
          height: 60,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            width: 60,
            height: 60,
            color: AppTheme.backgroundColor,
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.error_outline,
              color: AppTheme.errorColor,
              size: 24,
            ),
          ),
        ),
      );
    } else {
      final file = File(imagePath);
      if (file.existsSync()) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            file,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.error_outline,
                color: AppTheme.errorColor,
                size: 24,
              ),
            ),
          ),
        );
      } else {
        return Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.image_not_supported_outlined,
            color: AppTheme.textSecondary,
            size: 24,
          ),
        );
      }
    }
  }

  Widget _buildFlashcardItem(Flashcard flashcard, int index) {
    return Card(
      child: ListTile(
        leading: _buildFlashcardThumbnail(flashcard),
        title: Text(
          flashcard.word,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Created: ${_formatDate(flashcard.createdAt)}',
              style: const TextStyle(fontSize: 12),
            ),
            if (!flashcard.isSynced)
              const Text(
                'Not synced',
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _editFlashcard(flashcard),
              icon: const Icon(Icons.edit),
              color: AppTheme.primaryColor,
              tooltip: 'Edit',
            ),
            IconButton(
              onPressed: () => _deleteFlashcard(flashcard),
              icon: const Icon(Icons.delete),
              color: AppTheme.errorColor,
              tooltip: 'Delete',
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.list_outlined, size: 80, color: AppTheme.textSecondary),
          SizedBox(height: 16),
          Text(
            'No flashcards yet',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Tap the + button to add your first flashcard',
            style: TextStyle(fontSize: 16, color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Word List'),
          backgroundColor: AppTheme.primaryColor,
        ),
        body: const Center(
          child: CircularProgressIndicator(color: AppTheme.primaryColor),
        ),
      );
    }

    return Consumer<FlashcardService>(
      builder: (context, flashcardService, child) {
        final categories = flashcardService.categories;
        final flashcardsByCategory = <String, List<Flashcard>>{};
        for (final cat in categories) {
          flashcardsByCategory[cat.id] = flashcardService.flashcards
            .where((f) => f.categoryId == cat.id)
            .toList();
        }
        return Scaffold(
          appBar: AppBar(
            title: Text('Word List (${flashcardService.count})'),
            backgroundColor: AppTheme.primaryColor,
            actions: [
              IconButton(
                onPressed: _isSyncing ? null : _downloadFromFirebase,
                icon: _isSyncing
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.download),
                tooltip: 'Download from server',
              ),
              IconButton(
                onPressed: _addFlashcard,
                icon: const Icon(Icons.add),
                tooltip: 'Add flashcard',
              ),
            ],
          ),
          body: flashcardService.isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.primaryColor,
                  ),
                )
              : flashcardService.isEmpty
              ? _buildEmptyState()
              : ListView(
                  padding: const EdgeInsets.all(8),
                  children: [
                    for (final cat in categories)
                      _buildCategorySection(cat.name, cat.id, flashcardsByCategory[cat.id] ?? []),
                  ],
                ),
        );
      },
    );
  }
}
