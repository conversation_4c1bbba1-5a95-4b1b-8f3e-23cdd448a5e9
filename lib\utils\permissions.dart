import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionUtils {
  // Request camera permission
  static Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Camera permission error: $e');
      return false;
    }
  }

  // Request photo library permission
  static Future<bool> requestPhotoPermission() async {
    try {
      final status = await Permission.photos.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Photo permission error: $e');
      return false;
    }
  }

  // Request microphone permission (for TTS)
  static Future<bool> requestMicrophonePermission() async {
    try {
      final status = await Permission.microphone.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Microphone permission error: $e');
      return false;
    }
  }

  // Request storage permission (Android)
  static Future<bool> requestStoragePermission() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final status = await Permission.storage.request();
        return status == PermissionStatus.granted;
      }
      return true; // iOS doesn't need explicit storage permission
    } catch (e) {
      debugPrint('Storage permission error: $e');
      return false;
    }
  }

  // Check if camera permission is granted
  static Future<bool> hasCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Check camera permission error: $e');
      return false;
    }
  }

  // Check if photo permission is granted
  static Future<bool> hasPhotoPermission() async {
    try {
      final status = await Permission.photos.status;
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Check photo permission error: $e');
      return false;
    }
  }

  // Check if microphone permission is granted
  static Future<bool> hasMicrophonePermission() async {
    try {
      final status = await Permission.microphone.status;
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Check microphone permission error: $e');
      return false;
    }
  }

  // Request all necessary permissions
  static Future<Map<String, bool>> requestAllPermissions() async {
    final results = <String, bool>{};
    
    results['camera'] = await requestCameraPermission();
    results['photos'] = await requestPhotoPermission();
    results['microphone'] = await requestMicrophonePermission();
    results['storage'] = await requestStoragePermission();
    
    return results;
  }

  // Check all permissions status
  static Future<Map<String, bool>> checkAllPermissions() async {
    final results = <String, bool>{};
    
    results['camera'] = await hasCameraPermission();
    results['photos'] = await hasPhotoPermission();
    results['microphone'] = await hasMicrophonePermission();
    
    return results;
  }

  // Show permission rationale dialog
  static Future<bool> showPermissionRationale(
    String permissionName,
    String reason,
  ) async {
    // This would typically show a dialog explaining why the permission is needed
    // For now, we'll just return true to proceed with the permission request
    debugPrint('Permission rationale for $permissionName: $reason');
    return true;
  }
}
