import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import '../services/flashcard_service.dart';
import '../services/sync_service.dart';
import '../utils/family_id_utils.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String? _familyId;
  bool _editFamilyId = false;
  final TextEditingController _familyIdController = TextEditingController();
  final TextEditingController _addCategoryController = TextEditingController();
  String? _editingCategoryId;
  final TextEditingController _editCategoryController = TextEditingController();
  bool _isSyncing = false;
  bool _isClearing = false;

  @override
  void initState() {
    super.initState();
    _loadFamilyId();
  }

  Future<void> _loadFamilyId() async {
    final id = await FamilyIdUtils.getFamilyId();
    setState(() {
      _familyId = id;
      _familyIdController.text = id;
    });
  }

  Future<void> _saveFamilyId() async {
    final newId = _familyIdController.text.trim();
    if (newId.isNotEmpty) {
      await FamilyIdUtils.setFamilyId(newId);
      setState(() {
        _familyId = newId;
        _editFamilyId = false;
      });
    }
  }

  Future<void> _clearLocalData(BuildContext context) async {
    setState(() => _isClearing = true);
    await Provider.of<FlashcardService>(
      context,
      listen: false,
    ).clearAllFlashcards();
    // Optionally clear categories as well
    // await Provider.of<FlashcardService>(context, listen: false).clearAllCategories();
    setState(() => _isClearing = false);
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Local data cleared.')));
    }
  }

  Future<void> _syncNow(BuildContext context) async {
    setState(() => _isSyncing = true);
    final syncService = Provider.of<SyncService>(context, listen: false);
    final result = await syncService.fullSync();
    setState(() => _isSyncing = false);
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(result.message)));
    }
  }

  @override
  Widget build(BuildContext context) {
    final flashcardService = Provider.of<FlashcardService>(context);
    final categories = flashcardService.categories
        .where((c) => c.name.trim().isNotEmpty && c.name != 'All')
        .toList();
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Family ID Section
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Family Group',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: _editFamilyId
                        ? TextField(
                            controller: _familyIdController,
                            decoration: const InputDecoration(
                              labelText: 'Family ID',
                            ),
                            textAlign: TextAlign.center,
                          )
                        : SelectableText(
                            _familyId ?? '',
                            style: Theme.of(context).textTheme.headlineSmall,
                            textAlign: TextAlign.center,
                          ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.copy),
                    onPressed: _familyId == null || _familyId!.isEmpty
                        ? null
                        : () {
                            Clipboard.setData(ClipboardData(text: _familyId!));
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Family ID copied!'),
                              ),
                            );
                          },
                  ),
                  IconButton(
                    icon: Icon(_editFamilyId ? Icons.save : Icons.edit),
                    onPressed: () async {
                      if (_editFamilyId) {
                        await _saveFamilyId();
                      } else {
                        setState(() => _editFamilyId = true);
                      }
                    },
                  ),
                  if (_editFamilyId)
                    IconButton(
                      icon: const Icon(Icons.cancel),
                      onPressed: () {
                        setState(() {
                          _editFamilyId = false;
                          _familyIdController.text = _familyId ?? '';
                        });
                      },
                    ),
                ],
              ),
            ],
          ),
          const Divider(height: 32),
          // Category Management
          Text('Categories', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _addCategoryController,
                  decoration: const InputDecoration(labelText: 'Add Category'),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: () async {
                  final name = _addCategoryController.text.trim();
                  if (name.isNotEmpty && _familyId != null) {
                    await flashcardService.addCategory(
                      name: name,
                      familyId: _familyId!,
                    );
                    _addCategoryController.clear();
                  }
                },
              ),
            ],
          ),
          ...categories.map(
            (cat) => _editingCategoryId == cat.id
                ? ListTile(
                    title: TextField(
                      controller: _editCategoryController,
                      decoration: const InputDecoration(
                        labelText: 'Edit Category',
                      ),
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.save),
                          onPressed: () async {
                            final name = _editCategoryController.text.trim();
                            if (name.isNotEmpty) {
                              await flashcardService.updateCategory(
                                cat.copyWith(name: name),
                              );
                              setState(() => _editingCategoryId = null);
                            }
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.cancel),
                          onPressed: () =>
                              setState(() => _editingCategoryId = null),
                        ),
                      ],
                    ),
                  )
                : ListTile(
                    title: Text(cat.name),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () {
                            setState(() {
                              _editingCategoryId = cat.id;
                              _editCategoryController.text = cat.name;
                            });
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () async {
                            final confirm = await showDialog<bool>(
                              context: context,
                              builder: (ctx) => AlertDialog(
                                title: const Text('Delete Category'),
                                content: Text('Delete category "${cat.name}"?'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(ctx, false),
                                    child: const Text('Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: () => Navigator.pop(ctx, true),
                                    child: const Text('Delete'),
                                  ),
                                ],
                              ),
                            );
                            if (confirm == true) {
                              await flashcardService.deleteCategory(cat.id);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
          ),
          const Divider(height: 32),
          // Sync and Clear Data
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  icon: _isSyncing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.sync),
                  label: const Text('Sync Now'),
                  onPressed: _isSyncing ? null : () => _syncNow(context),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  icon: _isClearing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.delete_forever),
                  label: const Text('Clear Local Data'),
                  onPressed: _isClearing
                      ? null
                      : () => _clearLocalData(context),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
